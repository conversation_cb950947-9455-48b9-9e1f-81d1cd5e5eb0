/**
 * DNA Encryption Platform
 * Advanced file encryption using DNA sequence encoding
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @license MIT
 */

class DNAEncryptionApp {
    constructor() {
        this.dnaCodec = new DNACodec();
        this.cryptoEngine = new AdvancedCryptoEngine();
        this.fileHandler = new AdvancedFileHandler();
        this.storageManager = new StorageManager();
        this.uiManager = new UIManager();
        
        this.currentFile = null;
        this.isProcessing = false;
        
        // Developer info
        this.developer = 'Mezd';
        this.version = '2.0.0';
        
        // پیام‌های فارسی
        this.messages = {
            initSuccess: 'پلتفرم رمزگذاری DNA با موفقیت راه‌اندازی شد! - توسط Mezd',
            initError: 'خطا در راه‌اندازی برنامه: ',
            fileSelectSuccess: 'فایل "{fileName}" با موفقیت انتخاب شد',
            noFileSelected: 'لطفاً ابتدا یک فایل انتخاب کنید',
            noPassword: 'لطفاً رمز عبور را وارد کنید',
            noDnaSequence: 'لطفاً توالی DNA را وارد کنید',
            processingFile: 'در حال پردازش فایل...',
            encrypting: 'در حال رمزگذاری داده‌ها...',
            encodingDNA: 'در حال تبدیل به DNA...',
            encryptionSuccess: 'فایل با موفقیت به DNA رمزگذاری شد!',
            encryptionError: 'خطا در رمزگذاری: ',
            decodingDNA: 'در حال تبدیل از DNA...',
            decrypting: 'در حال رمزگشایی داده‌ها...',
            decryptionError: 'خطا در رمزگشایی: ',
            weakPassword: 'رمز عبور وارد شده ضعیف است. لطفاً یک رمز عبور قوی‌تر انتخاب کنید.'
        };
        
        // console.log(`🧬 DNA Encryption Platform v${this.version} - Developed by ${this.developer}`);
    }

    async initialize() {
        try {
            await this.storageManager.initialize();
            this.uiManager.initialize();
            this.setupEventListeners();
            this.uiManager.showSuccess(this.messages.initSuccess);
        } catch (error) {
            this.uiManager.showError(this.messages.initError + error.message);
        }
    }

    setupEventListeners() {
        // File input handler
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelect(e.target.files[0]);
            });
        }

        // Encryption button
        const encryptBtn = document.getElementById('encryptBtn');
        if (encryptBtn) {
            encryptBtn.addEventListener('click', () => {
                this.handleEncryption();
            });
        }

        // Decryption button
        const decryptBtn = document.getElementById('decryptBtn');
        if (decryptBtn) {
            decryptBtn.addEventListener('click', () => {
                this.handleDecryption();
            });
        }

        // Password strength indicator
        const encryptPassword = document.getElementById('encryptPassword');
        if (encryptPassword) {
            encryptPassword.addEventListener('input', (e) => {
                this.updatePasswordStrength(e.target.value);
            });
        }
    }

    handleFileSelect(file) {
        if (!file) return;

        try {
            this.fileHandler.validateFile(file);
            this.currentFile = file;
            
            const metadata = this.fileHandler.getFileMetadata(file);
            this.displayFileInfo(metadata);
            
            this.uiManager.showSuccess(this.messages.fileSelectSuccess.replace('{fileName}', file.name));
        } catch (error) {
            this.uiManager.showError(error.message);
        }
    }

    async handleEncryption() {
        if (!this.currentFile) {
            this.uiManager.showError(this.messages.noFileSelected);
            return;
        }

        const password = document.getElementById('encryptPassword').value;
        if (!password) {
            this.uiManager.showError(this.messages.noPassword);
            return;
        }
        // Check password strength
        const strength = this.calculatePasswordStrength(password);
        if (strength.level === 'weak') {
            this.uiManager.showError(this.messages.weakPassword);
            return;
        }

        if (this.isProcessing) return;
        this.isProcessing = true;

        try {
            this.uiManager.showProgress(this.messages.processingFile, 0);

            // Process file in chunks
            const fileData = await this.fileHandler.processLargeFile(
                this.currentFile,
                (progress) => this.uiManager.updateProgress(progress * 0.3)
            );

            this.uiManager.showProgress(this.messages.encrypting, 30);

            // Encrypt data
            const encryptedData = await this.cryptoEngine.encryptBeforeDNA(fileData, password);
            this.uiManager.updateProgress(60);

            // Encode to DNA
            const dnaSequence = this.dnaCodec.encodeWithErrorCorrection(encryptedData);
            this.uiManager.updateProgress(90);

            // Store encryption record
            const encryptionId = await this.storageManager.storeEncryption({
                fileName: this.currentFile.name,
                fileSize: this.currentFile.size,
                timestamp: Date.now(),
                dnaLength: dnaSequence.length
            });

            this.uiManager.updateProgress(100);
            this.uiManager.hideProgress();

            // Display results
            this.uiManager.displayEncryptionResult(dnaSequence, encryptionId);
            this.uiManager.showSuccess(this.messages.encryptionSuccess);

        } catch (error) {
            this.uiManager.hideProgress();
            this.uiManager.showError(this.messages.encryptionError + error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    async handleDecryption() {
        const dnaSequence = document.getElementById('dnaInput').value.trim();
        const password = document.getElementById('decryptPassword').value;

        if (!dnaSequence) {
            this.uiManager.showError(this.messages.noDnaSequence);
            return;
        }

        if (!password) {
            this.uiManager.showError(this.messages.noPassword);
            return;
        }

        if (this.isProcessing) return;
        this.isProcessing = true;

        try {
            this.uiManager.showProgress(this.messages.decodingDNA, 0);

            // Decode from DNA
            const encryptedData = this.dnaCodec.decodeWithErrorCorrection(dnaSequence);
            this.uiManager.updateProgress(30);

            this.uiManager.showProgress(this.messages.decrypting, 30);

            // Decrypt data
            const decryptedData = await this.cryptoEngine.decryptAfterDNA(encryptedData, password);
            this.uiManager.updateProgress(80);

            // Detect file type and create filename
            const fileType = this.fileHandler.detectFileType(decryptedData);
            const extension = this.getFileExtension(fileType);
            const filename = `decrypted_${Date.now()}${extension}`;

            this.uiManager.updateProgress(100);
            this.uiManager.hideProgress();

            // Offer download
            this.uiManager.offerDownload(decryptedData, filename);

        } catch (error) {
            this.uiManager.hideProgress();
            this.uiManager.showError(this.messages.decryptionError + error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    displayFileInfo(metadata) {
        const fileInfo = document.getElementById('fileInfo');
        if (fileInfo) {
            fileInfo.innerHTML = `
                <div class="file-info-card">
                    <h4>📄 File Information</h4>
                    <p><strong>Name:</strong> ${metadata.name}</p>
                    <p><strong>Size:</strong> ${this.formatFileSize(metadata.size)}</p>
                    <p><strong>Type:</strong> ${metadata.type}</p>
                    <p><strong>Modified:</strong> ${metadata.lastModifiedDate.toLocaleString()}</p>
                </div>
            `;
        }
    }

    updatePasswordStrength(password) {
        const strength = this.calculatePasswordStrength(password);
        const indicator = document.getElementById('passwordStrength');
        
        if (indicator) {
            indicator.className = `password-strength ${strength.level}`;
            indicator.textContent = strength.text;
        }
    }

    calculatePasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 8) score++;
        if (password.length >= 12) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/[0-9]/.test(password)) score++;
        if (/[^A-Za-z0-9]/.test(password)) score++;

        const levels = [
            { level: 'weak', text: 'Weak' },
            { level: 'weak', text: 'Weak' },
            { level: 'medium', text: 'Medium' },
            { level: 'medium', text: 'Medium' },
            { level: 'strong', text: 'Strong' },
            { level: 'strong', text: 'Very Strong' }
        ];

        return levels[Math.min(score, 5)];
    }

    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    getFileExtension(mimeType) {
        const extensions = {
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'application/pdf': '.pdf',
            'text/plain': '.txt',
            'application/zip': '.zip',
            'audio/mp3': '.mp3',
            'video/mp4': '.mp4'
        };
        if (!mimeType || !extensions[mimeType]) {
            return '.bin';
        }
        return extensions[mimeType];
    }
}

// Check for required classes existence
if (typeof DNACodec === 'undefined' || typeof AdvancedCryptoEngine === 'undefined' || typeof AdvancedFileHandler === 'undefined' || typeof StorageManager === 'undefined' || typeof UIManager === 'undefined') {
    alert('یکی از ماژول‌های اصلی برنامه بارگذاری نشده است. لطفاً صفحه را مجدداً بارگذاری کنید یا با پشتیبانی تماس بگیرید.');
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    window.dnaApp = new DNAEncryptionApp();
    await window.dnaApp.initialize();
});

